document.addEventListener('DOMContentLoaded', () => {
    // Check user authentication status
    checkUserStatus();

    const buttons = document.querySelectorAll('.menu-btn');

    // Add click sound effect (you can replace with actual audio file)
    const playClickSound = () => {
        // Create a simple beep sound using Web Audio API
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.value = 800;
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    };

    // Add button interactions
    buttons.forEach(button => {
        // Add hover sound effect
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-5px) scale(1.05)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translateY(0) scale(1)';
        });

        button.addEventListener('click', (e) => {
            playClickSound();

            // Add click animation
            button.style.transform = 'translateY(-2px) scale(1.02)';
            setTimeout(() => {
                button.style.transform = 'translateY(-5px) scale(1.05)';
            }, 100);

            const action = button.getAttribute('data-action');

            switch(action) {
                case 'play':
                    handlePlayGame();
                    break;
                case 'options':
                    console.log('Opening options...');
                    showMessage('Options menu coming soon!', 'info');
                    // Add options menu logic here
                    break;
                case 'credits':
                    console.log('Showing credits...');
                    showMessage('Credits: Made with ❤️', 'info');
                    // Add credits logic here
                    break;
                case 'signin':
                    window.location.href = 'auth.html';
                    break;
                case 'signout':
                    handleSignOut();
                    break;
            }
        });
    });

    // Create floating particles dynamically
    createFloatingParticles();

    // Message system
    function showMessage(text, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = text;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 1rem 2rem;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 5px;
            border-left: 4px solid ${type === 'success' ? '#50c878' : type === 'warning' ? '#ffa500' : '#4a90e2'};
            z-index: 1000;
            animation: slideDown 0.5s ease-out;
        `;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.style.animation = 'slideUp 0.5s ease-out forwards';
            setTimeout(() => messageDiv.remove(), 500);
        }, 2000);
    }

    // Add CSS animations for messages
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-100px); opacity: 0; }
            to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }
        @keyframes slideUp {
            from { transform: translateX(-50%) translateY(0); opacity: 1; }
            to { transform: translateX(-50%) translateY(-100px); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
});

function createFloatingParticles() {
    const particleCount = 20;
    const container = document.querySelector('.floating-particles');

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: ${Math.random() * 4 + 1}px;
            height: ${Math.random() * 4 + 1}px;
            background: rgba(74, 144, 226, ${Math.random() * 0.5 + 0.2});
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: float ${Math.random() * 10 + 10}s infinite ease-in-out;
            animation-delay: ${Math.random() * 5}s;
        `;
        container.appendChild(particle);
    }
}

// Check user authentication status
function checkUserStatus() {
    const sessionData = localStorage.getItem('userSession') || sessionStorage.getItem('userSession');
    const userStatusDiv = document.getElementById('user-status');
    const signinBtn = document.getElementById('signin-btn');

    if (sessionData) {
        const userData = JSON.parse(sessionData);

        // Show user info
        userStatusDiv.innerHTML = `
            <div class="user-info">
                <div class="user-welcome">Welcome back!</div>
                <div class="user-email">${userData.email}</div>
                <button class="sign-out-btn" onclick="handleSignOut()">Sign Out</button>
            </div>
        `;

        // Change Sign In button to user menu or hide it
        signinBtn.style.display = 'none';
    } else {
        userStatusDiv.innerHTML = '';
        signinBtn.style.display = 'block';
    }
}

// Handle play game action
function handlePlayGame() {
    const sessionData = localStorage.getItem('userSession') || sessionStorage.getItem('userSession');

    if (!sessionData) {
        showMessage('Please sign in to play!', 'warning');
        setTimeout(() => {
            window.location.href = 'auth.html';
        }, 2000);
        return;
    }

    console.log('Starting rhythm game...');
    showMessage('Loading rhythm game...', 'success');

    // Navigate to rhythm game
    setTimeout(() => {
        window.location.href = 'rhythm-game.html';
    }, 1500);
}

// Handle sign out
function handleSignOut() {
    localStorage.removeItem('userSession');
    sessionStorage.removeItem('userSession');

    showMessage('Signed out successfully!', 'info');

    // Refresh the page to update UI
    setTimeout(() => {
        location.reload();
    }, 1500);
}