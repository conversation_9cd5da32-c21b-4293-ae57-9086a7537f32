class RhythmGame {
    constructor() {
        this.score = 0;
        this.combo = 0;
        this.maxCombo = 0;
        this.totalNotes = 0;
        this.hitNotes = 0;
        this.isPlaying = false;
        this.isPaused = false;
        this.currentSong = null;
        this.gameSpeed = 2; // pixels per frame
        this.notes = [];
        this.gameLoop = null;
        
        this.tracks = ['a', 's', 'd', 'f'];
        this.trackElements = {};
        
        this.initializeGame();
        this.bindEvents();
    }
    
    initializeGame() {
        // Get track elements
        this.tracks.forEach(key => {
            this.trackElements[key] = document.querySelector(`[data-key="${key}"]`);
        });
        
        // Show instructions initially
        document.getElementById('instructions').style.display = 'flex';
    }
    
    bindEvents() {
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
        document.addEventListener('keyup', (e) => this.handleKeyRelease(e));
        
        // UI events
        document.getElementById('pause-btn').addEventListener('click', () => this.togglePause());
        document.getElementById('home-btn').addEventListener('click', () => this.goHome());
        document.getElementById('close-menu').addEventListener('click', () => this.closeSongMenu());
        document.getElementById('got-it').addEventListener('click', () => this.closeInstructions());
        
        // Song selection
        document.querySelectorAll('.song-item').forEach(item => {
            item.addEventListener('click', () => this.selectSong(item.dataset.song));
        });
        
        // Game over buttons
        document.getElementById('play-again').addEventListener('click', () => this.playAgain());
        document.getElementById('select-song').addEventListener('click', () => this.showSongMenu());
        document.getElementById('back-home').addEventListener('click', () => this.goHome());
    }
    
    handleKeyPress(e) {
        const key = e.key.toLowerCase();
        if (this.tracks.includes(key) && this.isPlaying && !this.isPaused) {
            this.trackElements[key].classList.add('active');
            this.checkHit(key);
        }
    }
    
    handleKeyRelease(e) {
        const key = e.key.toLowerCase();
        if (this.tracks.includes(key)) {
            this.trackElements[key].classList.remove('active');
        }
    }
    
    checkHit(key) {
        const track = this.trackElements[key];
        const hitZone = track.querySelector('.hit-zone');
        const hitZoneRect = hitZone.getBoundingClientRect();
        const notes = track.querySelectorAll('.note:not(.hit):not(.miss)');
        
        let bestNote = null;
        let bestDistance = Infinity;
        
        notes.forEach(note => {
            const noteRect = note.getBoundingClientRect();
            const distance = Math.abs(noteRect.bottom - hitZoneRect.bottom);
            
            if (distance < bestDistance && distance < 100) {
                bestDistance = distance;
                bestNote = note;
            }
        });
        
        if (bestNote) {
            this.hitNote(bestNote, bestDistance, key);
        }
    }
    
    hitNote(note, distance, key) {
        let hitType, points;
        
        if (distance < 20) {
            hitType = 'perfect';
            points = 100;
        } else if (distance < 40) {
            hitType = 'good';
            points = 75;
        } else if (distance < 60) {
            hitType = 'ok';
            points = 50;
        } else {
            hitType = 'miss';
            points = 0;
        }
        
        if (hitType !== 'miss') {
            this.combo++;
            this.hitNotes++;
            this.score += points + (this.combo * 5); // Combo bonus
            note.classList.add('hit');
        } else {
            this.combo = 0;
            note.classList.add('miss');
        }
        
        this.maxCombo = Math.max(this.maxCombo, this.combo);
        this.showHitEffect(key, hitType);
        this.updateUI();
        
        setTimeout(() => note.remove(), 300);
    }
    
    showHitEffect(key, hitType) {
        const track = this.trackElements[key];
        const effect = document.createElement('div');
        effect.className = `hit-effect ${hitType}`;
        effect.textContent = hitType.toUpperCase();
        
        track.appendChild(effect);
        setTimeout(() => effect.remove(), 800);
    }
    
    createNote(track, delay = 0) {
        setTimeout(() => {
            if (!this.isPlaying || this.isPaused) return;
            
            const trackElement = this.trackElements[track];
            const notesContainer = trackElement.querySelector('.notes-container');
            const note = document.createElement('div');
            note.className = 'note';
            
            const fallDuration = (trackElement.offsetHeight + 100) / this.gameSpeed * 16.67; // Convert to ms
            note.style.animationDuration = `${fallDuration}ms`;
            
            notesContainer.appendChild(note);
            this.notes.push(note);
            this.totalNotes++;
            
            // Remove note after animation
            setTimeout(() => {
                if (note.parentNode && !note.classList.contains('hit') && !note.classList.contains('miss')) {
                    note.classList.add('miss');
                    this.combo = 0;
                    this.updateUI();
                    setTimeout(() => note.remove(), 300);
                }
            }, fallDuration);
        }, delay);
    }
    
    selectSong(songId) {
        this.currentSong = this.getSongData(songId);
        document.getElementById('song-title').textContent = this.currentSong.title;
        document.getElementById('song-artist').textContent = this.currentSong.artist;
        this.closeSongMenu();
        this.startGame();
    }
    
    getSongData(songId) {
        const songs = {
            demo1: {
                title: 'Electronic Dreams',
                artist: 'Cyber Composer',
                duration: 30000, // 30 seconds for demo
                bpm: 120,
                pattern: [
                    { track: 'a', time: 1000 },
                    { track: 's', time: 1500 },
                    { track: 'd', time: 2000 },
                    { track: 'f', time: 2500 },
                    { track: 'a', time: 3000 },
                    { track: 's', time: 3500 },
                    { track: 'd', time: 4000 },
                    { track: 'f', time: 4500 },
                    // Add more patterns...
                ]
            },
            demo2: {
                title: 'Cyber Pulse',
                artist: 'Digital Artist',
                duration: 25000,
                bpm: 140,
                pattern: [
                    { track: 'a', time: 800 },
                    { track: 'd', time: 1200 },
                    { track: 's', time: 1600 },
                    { track: 'f', time: 2000 },
                    // Add more patterns...
                ]
            },
            demo3: {
                title: 'Neon Nights',
                artist: 'Synthwave Master',
                duration: 35000,
                bpm: 160,
                pattern: [
                    { track: 'a', time: 600 },
                    { track: 's', time: 900 },
                    { track: 'd', time: 1200 },
                    { track: 'f', time: 1500 },
                    { track: 'a', time: 1800 },
                    // Add more patterns...
                ]
            }
        };
        
        return songs[songId];
    }
    
    startGame() {
        this.resetGame();
        this.isPlaying = true;
        this.isPaused = false;
        
        // Generate notes based on song pattern
        this.generateNotes();
        
        // Start game loop
        this.gameLoop = setInterval(() => this.update(), 16.67); // 60 FPS
        
        // End game after song duration
        setTimeout(() => this.endGame(), this.currentSong.duration);
    }
    
    generateNotes() {
        // Generate a simple pattern for demo
        const noteInterval = 60000 / this.currentSong.bpm; // ms per beat
        const totalBeats = this.currentSong.duration / noteInterval;
        
        for (let i = 0; i < totalBeats; i++) {
            const track = this.tracks[Math.floor(Math.random() * this.tracks.length)];
            const delay = i * noteInterval;
            this.createNote(track, delay);
        }
    }
    
    update() {
        if (!this.isPlaying || this.isPaused) return;
        
        // Game update logic here
        // Notes are animated via CSS, so minimal update needed
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('combo').textContent = this.combo;
        
        const accuracy = this.totalNotes > 0 ? Math.round((this.hitNotes / this.totalNotes) * 100) : 100;
        document.getElementById('accuracy').textContent = `${accuracy}%`;
    }
    
    resetGame() {
        this.score = 0;
        this.combo = 0;
        this.maxCombo = 0;
        this.totalNotes = 0;
        this.hitNotes = 0;
        this.notes = [];
        
        // Clear existing notes
        document.querySelectorAll('.note').forEach(note => note.remove());
        document.querySelectorAll('.hit-effect').forEach(effect => effect.remove());
        
        this.updateUI();
    }
    
    togglePause() {
        this.isPaused = !this.isPaused;
        document.getElementById('pause-btn').textContent = this.isPaused ? '▶️' : '⏸️';
    }
    
    endGame() {
        this.isPlaying = false;
        if (this.gameLoop) {
            clearInterval(this.gameLoop);
            this.gameLoop = null;
        }
        
        this.showGameOver();
    }
    
    showGameOver() {
        const accuracy = this.totalNotes > 0 ? Math.round((this.hitNotes / this.totalNotes) * 100) : 100;
        let grade = 'F';
        
        if (accuracy >= 95) grade = 'S';
        else if (accuracy >= 90) grade = 'A';
        else if (accuracy >= 80) grade = 'B';
        else if (accuracy >= 70) grade = 'C';
        else if (accuracy >= 60) grade = 'D';
        
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('max-combo').textContent = this.maxCombo;
        document.getElementById('final-accuracy').textContent = `${accuracy}%`;
        document.getElementById('grade').textContent = grade;
        
        document.getElementById('game-over').classList.add('show');
    }
    
    playAgain() {
        document.getElementById('game-over').classList.remove('show');
        this.startGame();
    }
    
    showSongMenu() {
        document.getElementById('game-over').classList.remove('show');
        document.getElementById('song-menu').style.display = 'flex';
    }
    
    closeSongMenu() {
        document.getElementById('song-menu').style.display = 'none';
    }
    
    closeInstructions() {
        document.getElementById('instructions').style.display = 'none';
        document.getElementById('song-menu').style.display = 'flex';
    }
    
    goHome() {
        window.location.href = 'index.html';
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    new RhythmGame();
});
