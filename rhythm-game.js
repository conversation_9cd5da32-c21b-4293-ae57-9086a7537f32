class TableTennisRhythm {
    constructor() {
        // Game scores (first to 11 wins)
        this.playerScore = 0;
        this.computerScore = 0;
        this.currentRally = 0;
        this.maxRally = 0;
        this.totalPoints = 0;

        this.isPlaying = false;
        this.isPaused = false;
        this.currentMatch = null;
        this.gameLoop = null;

        // Player position (smooth movement)
        this.player = {
            x: 50, // percentage from left (0-100)
            y: 80, // percentage from top (fixed behind table)
            targetX: 50,
            targetY: 80,
            speed: 0.15 // movement speed
        };

        this.computer = {
            x: 50,
            y: 20,
            targetX: 50,
            speed: 0.1
        };

        // 3D Ball physics
        this.ball = {
            x: 50, // percentage from left (0-100)
            y: 50, // percentage from top (0-100)
            z: 0,  // height above table (0-100)

            velocityX: 0, // pixels per frame
            velocityY: 0,
            velocityZ: 0,

            gravity: 0.8, // downward acceleration
            bounce: 0.7,  // bounce dampening
            friction: 0.98, // air resistance

            isMoving: false,
            lastHitBy: null, // 'player' or 'computer'
            bounceCount: 0
        };

        // Input handling
        this.keys = {};

        this.initializeGame();
        this.bindEvents();
    }
    
    initializeGame() {
        console.log('Initializing table tennis game...');

        // Get game elements
        this.ballElement = document.getElementById('game-ball');
        this.humanPlayer = document.getElementById('human-player');
        this.computerPlayer = document.getElementById('computer-player');
        this.gameStatus = document.getElementById('game-status');

        console.log('Game elements:', {
            ball: this.ballElement,
            human: this.humanPlayer,
            computer: this.computerPlayer,
            status: this.gameStatus
        });

        // Show instructions initially
        const instructions = document.getElementById('instructions');
        if (instructions) {
            instructions.style.display = 'flex';
        }

        // Initialize positions
        this.resetBall();
        this.updateVisuals();

        console.log('Game initialized successfully');
    }
    
    bindEvents() {
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));

        // UI events
        document.getElementById('pause-btn').addEventListener('click', () => this.togglePause());
        document.getElementById('home-btn').addEventListener('click', () => this.goHome());
        document.getElementById('close-menu').addEventListener('click', () => this.closeSongMenu());
        document.getElementById('got-it').addEventListener('click', () => this.closeInstructions());

        // Match selection
        document.querySelectorAll('.song-item').forEach(item => {
            item.addEventListener('click', () => this.selectMatch(item.dataset.song));
        });

        // Game over buttons
        document.getElementById('play-again').addEventListener('click', () => this.playAgain());
        document.getElementById('select-song').addEventListener('click', () => this.showSongMenu());
        document.getElementById('back-home').addEventListener('click', () => this.goHome());
    }

    handleKeyDown(e) {
        this.keys[e.key.toLowerCase()] = true;

        if (e.key === ' ') {
            e.preventDefault();
            this.attemptHit();
        }
    }

    handleKeyUp(e) {
        this.keys[e.key.toLowerCase()] = false;
    }

    updatePlayerMovement() {
        if (!this.isPlaying || this.isPaused) return;

        // Handle continuous movement
        let moveX = 0;
        let moveY = 0;

        // WASD or Arrow keys
        if (this.keys['a'] || this.keys['arrowleft']) moveX -= 1;
        if (this.keys['d'] || this.keys['arrowright']) moveX += 1;
        if (this.keys['w'] || this.keys['arrowup']) moveY -= 1;
        if (this.keys['s'] || this.keys['arrowdown']) moveY += 1;

        // Update target position with faster movement
        this.player.targetX += moveX * 1.5;
        this.player.targetY += moveY * 1.0;

        // Constrain to player's side of court (behind the table)
        this.player.targetX = Math.max(5, Math.min(95, this.player.targetX));
        this.player.targetY = Math.max(55, Math.min(95, this.player.targetY)); // Keep behind center line

        // Direct movement for responsiveness
        this.player.x = this.player.targetX;
        this.player.y = this.player.targetY;
    }

    updateComputerAI() {
        if (!this.isPlaying || this.isPaused) return;

        // Computer tries to get to ball position when ball is on its side
        if (this.ball.y < 50 && this.ball.isMoving) {
            this.computer.targetX = this.ball.x;
        }

        // Smooth movement
        this.computer.x += (this.computer.targetX - this.computer.x) * this.computer.speed;
    }

    updateVisuals() {
        // Update player positions
        if (this.humanPlayer) {
            this.humanPlayer.style.left = `${this.player.x}%`;
            this.humanPlayer.style.bottom = `${100 - this.player.y}%`;
        }

        if (this.computerPlayer) {
            this.computerPlayer.style.left = `${this.computer.x}%`;
            this.computerPlayer.style.top = `${this.computer.y}%`;
        }

        // Update ball position with 3D effect
        if (this.ballElement) {
            this.ballElement.style.left = `${this.ball.x}%`;
            this.ballElement.style.top = `${this.ball.y}%`;
            this.ballElement.style.transform = `translateX(-50%) translateY(-50%) translateZ(${this.ball.z}px)`;
            this.ballElement.style.display = 'block';

            // Add shadow based on height
            const shadowSize = Math.max(10, 30 - this.ball.z * 0.3);
            const shadowOpacity = Math.max(0.1, 0.6 - this.ball.z * 0.01);
            this.ballElement.style.boxShadow = `
                0 0 25px rgba(255, 107, 53, 1.0),
                inset -4px -4px 8px rgba(0, 0, 0, 0.4),
                0 ${this.ball.z * 0.8}px ${shadowSize}px rgba(0, 0, 0, ${shadowOpacity})
            `;

            // Debug: log ball position occasionally
            if (Math.random() < 0.005) {
                console.log(`Ball: x=${this.ball.x.toFixed(1)}, y=${this.ball.y.toFixed(1)}, z=${this.ball.z.toFixed(1)}, moving=${this.ball.isMoving}`);
            }
        }
    }
    
    resetBall() {
        console.log('Resetting ball position');
        this.ball = {
            x: 50,
            y: 50,
            z: 30,
            velocityX: 0,
            velocityY: 0,
            velocityZ: 0,
            gravity: 0.8,
            bounce: 0.7,
            friction: 0.98,
            isMoving: false,
            lastHitBy: null,
            bounceCount: 0
        };

        // Make sure ball is visible
        if (this.ballElement) {
            this.ballElement.style.display = 'block';
        }
    }

    updateBallPhysics() {
        if (!this.ball.isMoving) return;

        // Apply gravity
        this.ball.velocityZ -= this.ball.gravity;

        // Update position
        this.ball.x += this.ball.velocityX;
        this.ball.y += this.ball.velocityY;
        this.ball.z += this.ball.velocityZ;

        // Apply air resistance
        this.ball.velocityX *= this.ball.friction;
        this.ball.velocityY *= this.ball.friction;

        // Bounce off table (z = 0)
        if (this.ball.z <= 0) {
            this.ball.z = 0;
            this.ball.velocityZ = -this.ball.velocityZ * this.ball.bounce;
            this.ball.bounceCount++;

            // Check if ball is out of bounds or missed
            this.checkBallBounds();
        }

        // Bounce off sides
        if (this.ball.x <= 5 || this.ball.x >= 95) {
            this.ball.velocityX = -this.ball.velocityX * 0.8;
            this.ball.x = Math.max(5, Math.min(95, this.ball.x));
        }

        // Check if ball went too far
        if (this.ball.y < -10 || this.ball.y > 110) {
            this.ballOutOfBounds();
        }
    }

    checkBallBounds() {
        // Check if ball bounced on player's side or computer's side
        if (this.ball.y > 50) {
            // Player's side
            if (this.ball.lastHitBy === 'computer') {
                // Computer hit it to player's side, player needs to return it
                this.waitingForPlayerHit = true;
            }
        } else {
            // Computer's side
            if (this.ball.lastHitBy === 'player') {
                // Player hit it to computer's side, computer will try to return it
                this.computerAttemptHit();
            }
        }
    }

    ballOutOfBounds() {
        this.ball.isMoving = false;

        // Determine who gets the point
        if (this.ball.y < 0) {
            // Ball went off computer's side
            if (this.ball.lastHitBy === 'player') {
                this.playerScore++;
                this.showHitEffect('POINT!', 'perfect');
            } else {
                this.computerScore++;
                this.showHitEffect('Computer Point!', 'miss');
            }
        } else {
            // Ball went off player's side
            if (this.ball.lastHitBy === 'computer') {
                this.computerScore++;
                this.showHitEffect('Computer Point!', 'miss');
            } else {
                this.playerScore++;
                this.showHitEffect('POINT!', 'perfect');
            }
        }

        this.totalPoints++;
        this.updateUI();
        this.checkGameEnd();
    }

    attemptHit() {
        if (!this.isPlaying || this.isPaused) return;

        // Handle serving
        if (this.waitingForServe) {
            this.ball.velocityX = (Math.random() - 0.5) * 0.4;
            this.ball.velocityY = -0.8 - (Math.random() * 0.3);
            this.ball.velocityZ = 5;
            this.ball.isMoving = true;
            this.ball.lastHitBy = 'player';
            this.waitingForServe = false;
            this.updateGameStatus("Ball in play!");
            return;
        }

        if (!this.ball.isMoving) return;

        // Check if ball is close enough to player
        const distanceToPlayer = Math.sqrt(
            Math.pow(this.ball.x - this.player.x, 2) +
            Math.pow(this.ball.y - this.player.y, 2)
        );

        if (distanceToPlayer > 15) {
            this.showHitEffect('TOO FAR!', 'miss');
            return;
        }

        // Check if ball is at reasonable height
        if (this.ball.z > 50) {
            this.showHitEffect('TOO HIGH!', 'miss');
            return;
        }

        // Calculate hit power based on timing and position
        const timingFactor = Math.max(0, 1 - distanceToPlayer / 15);
        const heightFactor = Math.max(0, 1 - this.ball.z / 50);
        const power = timingFactor * heightFactor;

        this.swingPaddle();
        this.currentRally++;
        this.maxRally = Math.max(this.maxRally, this.currentRally);

        // Hit the ball
        this.hitBall(power);

        let hitType, message;
        if (power > 0.8) {
            hitType = 'perfect';
            message = 'POWER SHOT!';
        } else if (power > 0.6) {
            hitType = 'good';
            message = 'GOOD HIT!';
        } else if (power > 0.3) {
            hitType = 'ok';
            message = 'Nice hit';
        } else {
            hitType = 'miss';
            message = 'Weak hit';
        }

        this.showHitEffect(message, hitType);
        this.updateUI();
    }

    hitBall(power) {
        // Calculate new ball velocity based on hit power and direction
        const baseSpeed = 0.8 + (power * 1.2);

        // Direction towards computer's side
        const targetX = 30 + (Math.random() * 40); // Random position on computer side
        const targetY = 20 + (Math.random() * 20);

        const deltaX = targetX - this.ball.x;
        const deltaY = targetY - this.ball.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        this.ball.velocityX = (deltaX / distance) * baseSpeed;
        this.ball.velocityY = (deltaY / distance) * baseSpeed;
        this.ball.velocityZ = 8 + (power * 5); // Upward velocity

        this.ball.lastHitBy = 'player';
        this.ball.bounceCount = 0;

        // Add visual effect
        this.ballElement.classList.add('hit-effect');
        setTimeout(() => this.ballElement.classList.remove('hit-effect'), 300);
    }

    computerAttemptHit() {
        // Simple AI: computer tries to hit ball if it's close enough
        const distanceToComputer = Math.sqrt(
            Math.pow(this.ball.x - this.computer.x, 2) +
            Math.pow(this.ball.y - this.computer.y, 2)
        );

        if (distanceToComputer < 20 && this.ball.z < 40) {
            this.computerHitBall();
        }
    }

    computerHitBall() {
        // Computer hits ball back to player
        const baseSpeed = 0.6 + (Math.random() * 0.4);

        // Aim towards player's side
        const targetX = 30 + (Math.random() * 40);
        const targetY = 70 + (Math.random() * 20);

        const deltaX = targetX - this.ball.x;
        const deltaY = targetY - this.ball.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        this.ball.velocityX = (deltaX / distance) * baseSpeed;
        this.ball.velocityY = (deltaY / distance) * baseSpeed;
        this.ball.velocityZ = 6 + (Math.random() * 4);

        this.ball.lastHitBy = 'computer';
        this.ball.bounceCount = 0;

        // Computer paddle swing animation
        const computerPaddle = this.computerPlayer.querySelector('.player-paddle');
        computerPaddle.classList.add('swing');
        setTimeout(() => computerPaddle.classList.remove('swing'), 300);
    }

    swingPaddle() {
        const paddle = this.humanPlayer.querySelector('.player-paddle');
        paddle.classList.add('swing');
        setTimeout(() => paddle.classList.remove('swing'), 300);
    }

    checkGameEnd() {
        if (this.playerScore >= 11 || this.computerScore >= 11) {
            // Game ends when someone reaches 11 points
            this.endGame();
        } else {
            // Start new point
            setTimeout(() => this.startNewPoint(), 2000);
        }
    }

    startNewPoint() {
        this.currentRally = 0;
        this.resetBall();

        // Serve alternates or computer serves first
        const serverMessage = (this.totalPoints % 2 === 0) ? "Computer serves!" : "Your serve!";
        this.updateGameStatus(serverMessage);

        setTimeout(() => {
            if (this.totalPoints % 2 === 0) {
                this.computerServe();
            } else {
                this.playerServe();
            }
        }, 1000);
    }

    computerServe() {
        console.log('Computer serving...');
        this.ball.x = this.computer.x;
        this.ball.y = this.computer.y + 10;
        this.ball.z = 30;

        // Serve towards player with more visible movement
        this.ball.velocityX = (Math.random() - 0.5) * 0.8;
        this.ball.velocityY = 1.2;
        this.ball.velocityZ = 8;
        this.ball.isMoving = true;
        this.ball.lastHitBy = 'computer';

        this.updateGameStatus("Return the serve!");
        console.log('Ball is now moving:', this.ball);
    }

    playerServe() {
        this.ball.x = this.player.x;
        this.ball.y = this.player.y - 5;
        this.ball.z = 20;
        this.ball.isMoving = false;

        this.updateGameStatus("Press SPACEBAR to serve!");

        // Wait for player to hit spacebar
        this.waitingForServe = true;
    }

    updateGameStatus(message) {
        const servingInfo = this.gameStatus.querySelector('.serving-info');
        if (servingInfo) {
            servingInfo.textContent = message;
        }
    }

    startGame() {
        console.log('Starting table tennis game...');
        this.resetGame();
        this.isPlaying = true;
        this.isPaused = false;

        // Make sure all elements are available
        if (!this.ballElement || !this.humanPlayer || !this.computerPlayer) {
            console.error('Game elements not found!');
            return;
        }

        // Start game loop
        this.gameLoop = setInterval(() => this.update(), 16.67); // 60 FPS

        // Start first point after a short delay
        setTimeout(() => {
            this.startNewPoint();
        }, 1000);
    }

    update() {
        if (!this.isPlaying || this.isPaused) return;

        // Update player movement
        this.updatePlayerMovement();

        // Update computer AI
        this.updateComputerAI();

        // Update ball physics
        this.updateBallPhysics();

        // Update all visual elements
        this.updateVisuals();

        // Debug ball position occasionally
        if (Math.random() < 0.01) { // 1% chance per frame
            console.log('Ball position:', this.ball.x, this.ball.y, this.ball.z, 'Moving:', this.ball.isMoving);
        }
    }

    resetGame() {
        this.playerScore = 0;
        this.computerScore = 0;
        this.currentRally = 0;
        this.maxRally = 0;
        this.totalPoints = 0;

        // Reset positions
        this.player.x = 50;
        this.player.y = 80;
        this.player.targetX = 50;
        this.player.targetY = 80;

        this.computer.x = 50;
        this.computer.y = 20;
        this.computer.targetX = 50;

        this.resetBall();

        // Clear effects
        document.querySelectorAll('.hit-effect').forEach(effect => effect.remove());

        this.updateVisuals();
        this.updateUI();
    }
    
    showHitEffect(text, hitType) {
        const scene = document.querySelector('.table-tennis-scene');
        const effect = document.createElement('div');
        effect.className = `hit-effect ${hitType}`;
        effect.textContent = text;

        scene.appendChild(effect);
        setTimeout(() => effect.remove(), 1200);
    }
    
    updateUI() {
        document.getElementById('player-score').textContent = this.playerScore;
        document.getElementById('computer-score').textContent = this.computerScore;
        document.getElementById('rally').textContent = this.currentRally;
    }
    
    showHitEffect(text, hitType) {
        const scene = document.querySelector('.table-tennis-scene');
        const effect = document.createElement('div');
        effect.className = `hit-effect ${hitType}`;
        effect.textContent = text;

        scene.appendChild(effect);
        setTimeout(() => effect.remove(), 1200);
    }
    
    startGame() {
        this.resetGame();
        this.isPlaying = true;
        this.isPaused = false;

        // Start first rally
        this.startNewRally();

        // Start game loop for continuous updates
        this.gameLoop = setInterval(() => this.update(), 16.67); // 60 FPS

        // End game after match duration
        setTimeout(() => this.endGame(), this.currentMatch.duration);
    }

    update() {
        if (!this.isPlaying || this.isPaused) return;

        // Update timing indicator
        this.updateTimingIndicator();
    }

    updateTimingIndicator() {
        if (!this.ball.isOnPlayerSide) {
            this.timingIndicator.style.opacity = '0.3';
            return;
        }

        this.timingIndicator.style.opacity = '1';

        // Update timing cursor based on ball bounce phase
        const cursor = document.getElementById('timing-cursor');
        if (cursor) {
            cursor.style.left = `${this.ball.bouncePhase * 96}%`;
        }
    }

    resetGame() {
        this.score = 0;
        this.rally = 0;
        this.maxRally = 0;
        this.totalHits = 0;
        this.successfulHits = 0;
        this.playerPosition = 0;
        this.computerPosition = 0;

        // Clear existing effects
        document.querySelectorAll('.hit-effect').forEach(effect => effect.remove());

        this.updateVisuals();
        this.updateUI();
    }
    
    selectMatch(matchId) {
        this.currentMatch = this.getMatchData(matchId);
        document.getElementById('song-title').textContent = this.currentMatch.opponent;
        document.getElementById('song-artist').textContent = this.currentMatch.difficulty;
        this.closeSongMenu();

        // Small delay to ensure UI is ready
        setTimeout(() => {
            this.startGame();
        }, 500);
    }
    
    getMatchData(matchId) {
        const matches = {
            demo1: {
                opponent: 'Rookie Robot',
                difficulty: 'Beginner',
                duration: 30000, // 30 seconds for demo
                ballInterval: 2500, // Ball every 2.5 seconds
                speed: 'slow'
            },
            demo2: {
                opponent: 'Speed Demon',
                difficulty: 'Intermediate',
                duration: 25000,
                ballInterval: 1800, // Ball every 1.8 seconds
                speed: 'medium'
            },
            demo3: {
                opponent: 'Spin Master',
                difficulty: 'Expert',
                duration: 35000,
                ballInterval: 1200, // Ball every 1.2 seconds
                speed: 'fast'
            }
        };

        return matches[matchId];
    }
    
    endGame() {
        this.isPlaying = false;
        if (this.gameLoop) {
            clearInterval(this.gameLoop);
            this.gameLoop = null;
        }

        this.showGameOver();
    }

    showGameOver() {
        const winner = this.playerScore > this.computerScore ? 'You Win!' : 'Computer Wins!';

        document.getElementById('final-player-score').textContent = this.playerScore;
        document.getElementById('final-computer-score').textContent = this.computerScore;
        document.getElementById('winner').textContent = winner;
        document.getElementById('max-rally').textContent = this.maxRally;
        document.getElementById('total-points').textContent = this.totalPoints;

        document.getElementById('game-over').classList.add('show');
    }
    
    togglePause() {
        this.isPaused = !this.isPaused;
        document.getElementById('pause-btn').textContent = this.isPaused ? '▶️' : '⏸️';
    }
    
    playAgain() {
        document.getElementById('game-over').classList.remove('show');
        this.startGame();
    }
    
    showSongMenu() {
        document.getElementById('game-over').classList.remove('show');
        document.getElementById('song-menu').style.display = 'flex';
    }
    
    closeSongMenu() {
        document.getElementById('song-menu').style.display = 'none';
    }
    
    closeInstructions() {
        document.getElementById('instructions').style.display = 'none';
        const songMenu = document.getElementById('song-menu');
        if (songMenu) {
            songMenu.style.display = 'flex';
        } else {
            // If no song menu, start the game directly
            console.log('No song menu found, starting game directly');
            this.currentMatch = this.getMatchData('demo1');
            this.startGame();
        }
    }
    
    goHome() {
        window.location.href = 'index.html';
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    new TableTennisRhythm();
});
