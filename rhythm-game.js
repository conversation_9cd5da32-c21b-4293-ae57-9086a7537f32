class TableTennisRhythm {
    constructor() {
        this.score = 0;
        this.rally = 0;
        this.maxRally = 0;
        this.totalBalls = 0;
        this.hitBalls = 0;
        this.isPlaying = false;
        this.isPaused = false;
        this.currentMatch = null;
        this.balls = [];
        this.gameLoop = null;

        this.shots = ['a', 's', 'd', 'f'];
        this.shotTypes = {
            'a': 'forehand',
            's': 'backhand',
            'd': 'smash',
            'f': 'spin'
        };
        this.timingZones = {};

        this.initializeGame();
        this.bindEvents();
    }
    
    initializeGame() {
        // Get timing zone elements
        this.shots.forEach(key => {
            this.timingZones[key] = document.querySelector(`[data-key="${key}"]`);
        });

        // Show instructions initially
        document.getElementById('instructions').style.display = 'flex';
    }
    
    bindEvents() {
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
        document.addEventListener('keyup', (e) => this.handleKeyRelease(e));
        
        // UI events
        document.getElementById('pause-btn').addEventListener('click', () => this.togglePause());
        document.getElementById('home-btn').addEventListener('click', () => this.goHome());
        document.getElementById('close-menu').addEventListener('click', () => this.closeSongMenu());
        document.getElementById('got-it').addEventListener('click', () => this.closeInstructions());
        
        // Match selection
        document.querySelectorAll('.song-item').forEach(item => {
            item.addEventListener('click', () => this.selectMatch(item.dataset.song));
        });
        
        // Game over buttons
        document.getElementById('play-again').addEventListener('click', () => this.playAgain());
        document.getElementById('select-song').addEventListener('click', () => this.showSongMenu());
        document.getElementById('back-home').addEventListener('click', () => this.goHome());
    }
    
    handleKeyPress(e) {
        const key = e.key.toLowerCase();
        if (this.shots.includes(key) && this.isPlaying && !this.isPaused) {
            this.timingZones[key].classList.add('active');
            this.swingPaddle();
            this.checkHit(key);
        }
    }

    handleKeyRelease(e) {
        const key = e.key.toLowerCase();
        if (this.shots.includes(key)) {
            this.timingZones[key].classList.remove('active');
        }
    }

    swingPaddle() {
        const paddle = document.getElementById('player-paddle');
        paddle.classList.add('swing');
        setTimeout(() => paddle.classList.remove('swing'), 200);
    }
    
    checkHit(key) {
        const balls = document.querySelectorAll('.ball:not(.hit):not(.miss)');

        let bestBall = null;
        let bestDistance = Infinity;

        balls.forEach(ball => {
            const ballRect = ball.getBoundingClientRect();
            const courtRect = document.querySelector('.table-tennis-court').getBoundingClientRect();

            // Check if ball is in hitting zone (bottom 30% of court)
            const hitZoneTop = courtRect.bottom - (courtRect.height * 0.3);

            if (ballRect.top >= hitZoneTop) {
                const distance = Math.abs(ballRect.bottom - courtRect.bottom);

                if (distance < bestDistance && distance < 150) {
                    bestDistance = distance;
                    bestBall = ball;
                }
            }
        });

        if (bestBall) {
            this.hitBall(bestBall, bestDistance, key);
        }
    }
    
    hitBall(ball, distance, key) {
        let hitType, points, shotPower;

        if (distance < 30) {
            hitType = 'perfect';
            points = 150;
            shotPower = 'POWER SHOT!';
        } else if (distance < 60) {
            hitType = 'good';
            points = 100;
            shotPower = 'GOOD HIT!';
        } else if (distance < 90) {
            hitType = 'ok';
            points = 50;
            shotPower = 'Weak hit';
        } else {
            hitType = 'miss';
            points = 0;
            shotPower = 'MISS!';
        }

        if (hitType !== 'miss') {
            this.rally++;
            this.hitBalls++;
            this.score += points + (this.rally * 10); // Rally bonus
            ball.classList.add('hit');
        } else {
            this.rally = 0;
            ball.classList.add('miss');
        }

        this.maxRally = Math.max(this.maxRally, this.rally);
        this.showHitEffect(shotPower, hitType);
        this.showTimingFeedback(key, hitType);
        this.updateUI();

        setTimeout(() => ball.remove(), 500);
    }

    showTimingFeedback(key, hitType) {
        const zone = this.timingZones[key];
        zone.classList.add(hitType);
        setTimeout(() => zone.classList.remove(hitType), 500);
    }
    
    showHitEffect(text, hitType) {
        const court = document.querySelector('.table-tennis-court');
        const effect = document.createElement('div');
        effect.className = `hit-effect ${hitType}`;
        effect.textContent = text;

        court.appendChild(effect);
        setTimeout(() => effect.remove(), 1000);
    }
    
    createBall(delay = 0) {
        setTimeout(() => {
            if (!this.isPlaying || this.isPaused) return;

            const ballContainer = document.getElementById('ball-container');
            const ball = document.createElement('div');
            ball.className = 'ball';

            // Random horizontal position for variety
            const randomOffset = (Math.random() - 0.5) * 100; // -50px to +50px
            ball.style.left = `calc(50% + ${randomOffset}px)`;

            const bounceDuration = 3000 + (Math.random() * 1000); // 3-4 seconds
            ball.style.animationDuration = `${bounceDuration}ms`;

            ballContainer.appendChild(ball);
            this.balls.push(ball);
            this.totalBalls++;

            // Remove ball after animation if not hit
            setTimeout(() => {
                if (ball.parentNode && !ball.classList.contains('hit') && !ball.classList.contains('miss')) {
                    ball.classList.add('miss');
                    this.rally = 0;
                    this.showHitEffect('Ball missed!', 'miss');
                    this.updateUI();
                    setTimeout(() => ball.remove(), 1000);
                }
            }, bounceDuration);
        }, delay);
    }
    
    selectMatch(matchId) {
        this.currentMatch = this.getMatchData(matchId);
        document.getElementById('song-title').textContent = this.currentMatch.opponent;
        document.getElementById('song-artist').textContent = this.currentMatch.difficulty;
        this.closeSongMenu();
        this.startGame();
    }
    
    getMatchData(matchId) {
        const matches = {
            demo1: {
                opponent: 'Rookie Robot',
                difficulty: 'Beginner',
                duration: 30000, // 30 seconds for demo
                ballInterval: 2500, // Ball every 2.5 seconds
                speed: 'slow'
            },
            demo2: {
                opponent: 'Speed Demon',
                difficulty: 'Intermediate',
                duration: 25000,
                ballInterval: 1800, // Ball every 1.8 seconds
                speed: 'medium'
            },
            demo3: {
                opponent: 'Spin Master',
                difficulty: 'Expert',
                duration: 35000,
                ballInterval: 1200, // Ball every 1.2 seconds
                speed: 'fast'
            }
        };

        return matches[matchId];
    }
    
    startGame() {
        this.resetGame();
        this.isPlaying = true;
        this.isPaused = false;

        // Generate balls based on match difficulty
        this.generateBalls();

        // Start game loop
        this.gameLoop = setInterval(() => this.update(), 16.67); // 60 FPS

        // End game after match duration
        setTimeout(() => this.endGame(), this.currentMatch.duration);
    }

    generateBalls() {
        const totalBalls = Math.floor(this.currentMatch.duration / this.currentMatch.ballInterval);

        for (let i = 0; i < totalBalls; i++) {
            const delay = i * this.currentMatch.ballInterval + (Math.random() * 500); // Add some randomness
            this.createBall(delay);
        }
    }
    
    update() {
        if (!this.isPlaying || this.isPaused) return;
        
        // Game update logic here
        // Notes are animated via CSS, so minimal update needed
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('combo').textContent = this.rally;

        const hitRate = this.totalBalls > 0 ? Math.round((this.hitBalls / this.totalBalls) * 100) : 100;
        document.getElementById('accuracy').textContent = `${hitRate}%`;
    }

    resetGame() {
        this.score = 0;
        this.rally = 0;
        this.maxRally = 0;
        this.totalBalls = 0;
        this.hitBalls = 0;
        this.balls = [];

        // Clear existing balls and effects
        document.querySelectorAll('.ball').forEach(ball => ball.remove());
        document.querySelectorAll('.hit-effect').forEach(effect => effect.remove());

        this.updateUI();
    }
    
    togglePause() {
        this.isPaused = !this.isPaused;
        document.getElementById('pause-btn').textContent = this.isPaused ? '▶️' : '⏸️';
    }
    
    endGame() {
        this.isPlaying = false;
        if (this.gameLoop) {
            clearInterval(this.gameLoop);
            this.gameLoop = null;
        }
        
        this.showGameOver();
    }
    
    showGameOver() {
        const hitRate = this.totalBalls > 0 ? Math.round((this.hitBalls / this.totalBalls) * 100) : 100;
        let grade = 'F';

        if (hitRate >= 95) grade = 'S';
        else if (hitRate >= 90) grade = 'A';
        else if (hitRate >= 80) grade = 'B';
        else if (hitRate >= 70) grade = 'C';
        else if (hitRate >= 60) grade = 'D';

        document.getElementById('final-score').textContent = this.score;
        document.getElementById('max-combo').textContent = this.maxRally;
        document.getElementById('final-accuracy').textContent = `${hitRate}%`;
        document.getElementById('grade').textContent = grade;

        document.getElementById('game-over').classList.add('show');
    }
    
    playAgain() {
        document.getElementById('game-over').classList.remove('show');
        this.startGame();
    }
    
    showSongMenu() {
        document.getElementById('game-over').classList.remove('show');
        document.getElementById('song-menu').style.display = 'flex';
    }
    
    closeSongMenu() {
        document.getElementById('song-menu').style.display = 'none';
    }
    
    closeInstructions() {
        document.getElementById('instructions').style.display = 'none';
        document.getElementById('song-menu').style.display = 'flex';
    }
    
    goHome() {
        window.location.href = 'index.html';
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    new TableTennisRhythm();
});
