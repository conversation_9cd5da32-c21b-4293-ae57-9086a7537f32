<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Table Tennis Game</title>
    <link rel="stylesheet" href="rhythm-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <div class="game-container">
        <!-- Game Header -->
        <div class="game-header">
            <div class="score-section">
                <div class="player-score">You: <span id="player-score">0</span></div>
                <div class="computer-score">CPU: <span id="computer-score">0</span></div>
                <div class="rally-count">Rally: <span id="rally">0</span></div>
            </div>
            <div class="song-info">
                <div class="song-title" id="song-title">Test Match</div>
                <div class="song-artist" id="song-artist">Beginner</div>
            </div>
            <div class="game-controls">
                <button class="control-btn" id="pause-btn">⏸️</button>
                <button class="control-btn" id="home-btn">🏠</button>
            </div>
        </div>

        <!-- Game Area -->
        <div class="game-area">
            <!-- 3D Table Tennis Scene -->
            <div class="table-tennis-scene">
                <!-- 3D Table -->
                <div class="table-3d">
                    <div class="table-surface">
                        <div class="center-line"></div>
                        <div class="side-lines"></div>
                    </div>
                    <div class="table-net">
                        <div class="net-mesh"></div>
                        <div class="net-posts"></div>
                    </div>
                    <div class="table-legs">
                        <div class="leg leg-1"></div>
                        <div class="leg leg-2"></div>
                        <div class="leg leg-3"></div>
                        <div class="leg leg-4"></div>
                    </div>
                </div>
                
                <!-- Computer Player -->
                <div class="computer-player" id="computer-player">
                    <div class="player-body">🤖</div>
                    <div class="player-paddle">🏓</div>
                </div>
                
                <!-- Human Player -->
                <div class="human-player" id="human-player">
                    <div class="player-body">🧑</div>
                    <div class="player-paddle" id="player-paddle">🏓</div>
                </div>
                
                <!-- Ball -->
                <div class="ball" id="game-ball"></div>
                
                <!-- Movement Controls -->
                <div class="controls-overlay">
                    <div class="movement-instruction">
                        Use WASD or Arrow Keys to Move | SPACEBAR to Hit
                    </div>
                    <div class="game-status" id="game-status">
                        <div class="serving-info">Press any key to start!</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple test script to start the game immediately
        let game;

        document.addEventListener('DOMContentLoaded', () => {
            console.log('Test page loaded, starting game...');

            // Create a simple match data
            const testMatch = {
                opponent: 'Test Robot',
                difficulty: 'Beginner',
                duration: 120000, // 2 minutes
                ballInterval: 3000,
                speed: 'slow'
            };

            // Initialize the game
            game = new TableTennisRhythm();
            game.currentMatch = testMatch;

            // Hide instructions and start immediately
            const instructions = document.getElementById('instructions');
            if (instructions) {
                instructions.style.display = 'none';
            }

            // Start the game after elements are ready
            setTimeout(() => {
                console.log('Starting test game...');
                game.startGame();

                // Force a ball serve to test
                setTimeout(() => {
                    console.log('Force starting ball movement...');
                    game.ball.x = 50;
                    game.ball.y = 30;
                    game.ball.z = 40;
                    game.ball.velocityX = 0.5;
                    game.ball.velocityY = 1.0;
                    game.ball.velocityZ = 8;
                    game.ball.isMoving = true;
                    game.ball.lastHitBy = 'computer';
                }, 3000);
            }, 1000);
        });

        // Manual controls for testing
        document.addEventListener('keydown', (e) => {
            if (e.key === 'r' || e.key === 'R') {
                console.log('Restarting game...');
                if (game) {
                    game.startGame();
                }
            }
            if (e.key === 'b' || e.key === 'B') {
                console.log('Starting ball manually...');
                if (game) {
                    game.ball.isMoving = true;
                    game.ball.velocityY = 1.0;
                    game.ball.velocityZ = 5;
                }
            }
        });
    </script>
    <script src="rhythm-game.js"></script>
</body>
</html>
