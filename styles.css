* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    height: 100vh;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

/* Animated Background */
.background-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.stars {
    position: absolute;
    width: 100%;
    height: 100%;
    background: transparent;
}

.stars::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
}

@keyframes sparkle {
    from { transform: translateX(0); }
    to { transform: translateX(-200px); }
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-particles::before,
.floating-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(74, 144, 226, 0.6);
    border-radius: 50%;
    animation: float 15s infinite ease-in-out;
}

.floating-particles::before {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.floating-particles::after {
    top: 60%;
    right: 20%;
    animation-delay: 7s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
    10% { opacity: 1; }
    50% { transform: translateY(-100px) rotate(180deg); opacity: 1; }
    90% { opacity: 1; }
}

/* Main Content */
.home-screen {
    text-align: center;
    color: white;
    z-index: 1;
    animation: fadeInUp 2s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.logo-container {
    margin-bottom: 3rem;
    animation: glow 3s ease-in-out infinite alternate;
}

.game-title {
    font-size: 4.5rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #4a90e2, #50c878, #4a90e2);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(74, 144, 226, 0.5);
}

.subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
    letter-spacing: 2px;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes glow {
    from { filter: drop-shadow(0 0 20px rgba(74, 144, 226, 0.3)); }
    to { filter: drop-shadow(0 0 40px rgba(74, 144, 226, 0.6)); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* Menu Styles */
.menu {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.menu-btn {
    position: relative;
    padding: 1.2rem 3rem;
    font-size: 1.3rem;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    background: rgba(0, 0, 0, 0.4);
    color: white;
    border: 2px solid rgba(74, 144, 226, 0.5);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    backdrop-filter: blur(10px);
    transform: translateY(0);
    animation: slideInLeft 1s ease-out forwards;
    opacity: 0;
}

.menu-btn:nth-child(1) { animation-delay: 0.2s; }
.menu-btn:nth-child(2) { animation-delay: 0.4s; }
.menu-btn:nth-child(3) { animation-delay: 0.6s; }
.menu-btn:nth-child(4) { animation-delay: 0.8s; }

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.menu-btn.primary {
    border-color: #50c878;
    background: rgba(80, 200, 120, 0.1);
}

.menu-btn .btn-text {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.menu-btn .btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.4), transparent);
    transition: left 0.5s ease;
}

.menu-btn:hover {
    transform: translateY(-5px) scale(1.05);
    border-color: #4a90e2;
    box-shadow:
        0 10px 25px rgba(74, 144, 226, 0.3),
        0 0 50px rgba(74, 144, 226, 0.2);
    background: rgba(74, 144, 226, 0.2);
}

.menu-btn.primary:hover {
    border-color: #50c878;
    box-shadow:
        0 10px 25px rgba(80, 200, 120, 0.3),
        0 0 50px rgba(80, 200, 120, 0.2);
    background: rgba(80, 200, 120, 0.2);
}

.menu-btn:hover .btn-glow {
    left: 100%;
}

.menu-btn:active {
    transform: translateY(-2px) scale(1.02);
}

/* Version Info */
.version-info {
    position: absolute;
    bottom: 20px;
    right: 20px;
    color: rgba(255, 255, 255, 0.4);
    font-size: 0.9rem;
    font-weight: 400;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 3rem;
    }

    .menu-btn {
        padding: 1rem 2rem;
        font-size: 1.1rem;
    }

    .subtitle {
        font-size: 1rem;
    }
}