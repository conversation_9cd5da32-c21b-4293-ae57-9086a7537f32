body {
    margin: 0;
    padding: 0;
    font-family: 'Arial', sans-serif;
    background: #121212 url('background.jpg') no-repeat center center fixed;
    background-size: cover;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.home-screen {
    text-align: center;
    color: white;
}

.game-title {
    font-size: 5rem;
    margin-bottom: 2rem;
    text-shadow: 0 0 10px rgba(0, 100, 255, 0.8);
}

.menu {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.menu-btn {
    padding: 1rem 2rem;
    font-size: 1.5rem;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border: 2px solid #4a90e2;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
}

.menu-btn:hover {
    background-color: #4a90e2;
    transform: scale(1.05);
}