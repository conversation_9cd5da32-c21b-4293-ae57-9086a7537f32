* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    height: 100vh;
    overflow: hidden;
    color: white;
}

.game-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Game Header */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(74, 144, 226, 0.3);
}

.score-section {
    display: flex;
    gap: 2rem;
    font-weight: 600;
}

.score, .combo, .accuracy {
    color: #4a90e2;
}

.song-info {
    text-align: center;
}

.song-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #50c878;
}

.song-artist {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.game-controls {
    display: flex;
    gap: 1rem;
}

.control-btn {
    background: rgba(74, 144, 226, 0.2);
    border: 1px solid rgba(74, 144, 226, 0.5);
    border-radius: 8px;
    padding: 0.5rem;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(74, 144, 226, 0.4);
    transform: scale(1.1);
}

/* Game Area */
.game-area {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    perspective: 1000px;
}

.table-tennis-court {
    width: 90%;
    height: 80vh;
    position: relative;
    transform: rotateX(15deg);
}

.court-background {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #0d4f3c 0%, #1a7a5e 50%, #0d4f3c 100%);
    border: 4px solid #ffffff;
    border-radius: 20px;
    position: relative;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 0 50px rgba(255, 255, 255, 0.1);
}

.net {
    position: absolute;
    top: 48%;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, transparent, #ffffff, transparent);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.court-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.court-lines::before {
    content: '';
    position: absolute;
    top: 25%;
    left: 10%;
    right: 10%;
    height: 2px;
    background: rgba(255, 255, 255, 0.6);
}

.court-lines::after {
    content: '';
    position: absolute;
    bottom: 25%;
    left: 10%;
    right: 10%;
    height: 2px;
    background: rgba(255, 255, 255, 0.6);
}

/* Player Paddle */
.player-paddle {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 3rem;
    z-index: 10;
    transition: all 0.2s ease;
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

.player-paddle.swing {
    transform: translateX(-50%) rotate(45deg) scale(1.2);
}

/* Ball */
.ball-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.ball {
    position: absolute;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle at 30% 30%, #ffffff, #ff6b35);
    border-radius: 50%;
    box-shadow:
        0 0 15px rgba(255, 107, 53, 0.6),
        inset -3px -3px 5px rgba(0, 0, 0, 0.3);
    z-index: 5;
    animation: ballBounce linear;
}

.ball.hit {
    animation: ballHit 0.5s ease-out forwards;
}

.ball.miss {
    animation: ballMiss 1s ease-out forwards;
}

@keyframes ballBounce {
    0% {
        top: 10%;
        left: 50%;
        transform: translateX(-50%) scale(0.8);
    }
    25% {
        top: 30%;
        transform: translateX(-50%) scale(1);
    }
    50% {
        top: 50%;
        transform: translateX(-50%) scale(1.1);
    }
    75% {
        top: 70%;
        transform: translateX(-50%) scale(1);
    }
    100% {
        top: 85%;
        transform: translateX(-50%) scale(0.9);
    }
}

@keyframes ballHit {
    0% { transform: translateX(-50%) scale(1); opacity: 1; }
    50% { transform: translateX(-50%) scale(1.5) rotate(180deg); opacity: 0.8; }
    100% { transform: translateX(-50%) scale(0.5) rotate(360deg); opacity: 0; }
}

@keyframes ballMiss {
    0% { transform: translateX(-50%) scale(1); opacity: 1; }
    100% { transform: translateX(-50%) translateY(100px) scale(0.3); opacity: 0; }
}

/* Timing Zones */
.timing-zones {
    position: absolute;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    z-index: 15;
}

.timing-zone {
    width: 100px;
    height: 80px;
    background: rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.timing-zone:nth-child(1) { border-color: rgba(255, 77, 87, 0.5); }
.timing-zone:nth-child(2) { border-color: rgba(255, 165, 0, 0.5); }
.timing-zone:nth-child(3) { border-color: rgba(50, 205, 50, 0.5); }
.timing-zone:nth-child(4) { border-color: rgba(74, 144, 226, 0.5); }

.timing-zone.active {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.zone-key {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.2rem;
}

.zone-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
}

.hit-indicator {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    opacity: 0;
    transition: all 0.3s ease;
}

.timing-zone.perfect .hit-indicator {
    background: #50c878;
    opacity: 1;
    animation: perfectHit 0.5s ease-out;
}

.timing-zone.good .hit-indicator {
    background: #4a90e2;
    opacity: 1;
    animation: goodHit 0.5s ease-out;
}

.timing-zone.ok .hit-indicator {
    background: #ffa500;
    opacity: 1;
    animation: okHit 0.5s ease-out;
}

.timing-zone.miss .hit-indicator {
    background: #ff4757;
    opacity: 1;
    animation: missHit 0.5s ease-out;
}

@keyframes perfectHit {
    0% { transform: translateX(-50%) scale(0); }
    50% { transform: translateX(-50%) scale(1.5); }
    100% { transform: translateX(-50%) scale(1); opacity: 0; }
}

@keyframes goodHit {
    0% { transform: translateX(-50%) scale(0); }
    50% { transform: translateX(-50%) scale(1.3); }
    100% { transform: translateX(-50%) scale(1); opacity: 0; }
}

@keyframes okHit {
    0% { transform: translateX(-50%) scale(0); }
    50% { transform: translateX(-50%) scale(1.1); }
    100% { transform: translateX(-50%) scale(1); opacity: 0; }
}

@keyframes missHit {
    0% { transform: translateX(-50%) scale(0) rotate(0deg); }
    100% { transform: translateX(-50%) scale(0.5) rotate(180deg); opacity: 0; }
}

/* Hit Effects */
.hit-effect {
    position: absolute;
    bottom: 200px;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 700;
    font-size: 2rem;
    z-index: 20;
    pointer-events: none;
    animation: hitEffect 1s ease-out forwards;
    text-shadow: 0 0 10px currentColor;
}

.hit-effect.perfect { color: #50c878; }
.hit-effect.good { color: #4a90e2; }
.hit-effect.ok { color: #ffa500; }
.hit-effect.miss { color: #ff4757; }

@keyframes hitEffect {
    0% { opacity: 1; transform: translateX(-50%) translateY(0) scale(1); }
    100% { opacity: 0; transform: translateX(-50%) translateY(-80px) scale(1.8); }
}

/* Song Menu */
.song-menu {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid rgba(74, 144, 226, 0.3);
}

.menu-header h2 {
    color: #4a90e2;
    font-size: 2rem;
}

.close-menu {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-menu:hover {
    background: rgba(255, 77, 87, 0.2);
    color: #ff4757;
}

.song-list {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.song-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(74, 144, 226, 0.3);
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.song-item:hover {
    background: rgba(74, 144, 226, 0.1);
    transform: translateX(10px);
    box-shadow: 0 5px 20px rgba(74, 144, 226, 0.2);
}

.song-info-item {
    flex: 1;
}

.song-name {
    font-size: 1.3rem;
    font-weight: 700;
    color: #50c878;
    margin-bottom: 0.5rem;
}

.song-difficulty {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.song-preview {
    font-size: 2rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.song-item:hover .song-preview {
    opacity: 1;
    transform: scale(1.2);
}

/* Game Over Screen */
.game-over {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.game-over.show {
    display: flex;
    animation: fadeIn 0.5s ease-out;
}

.game-over-content {
    text-align: center;
    background: rgba(0, 0, 0, 0.4);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(74, 144, 226, 0.3);
}

.game-over-content h2 {
    color: #50c878;
    font-size: 2.5rem;
    margin-bottom: 2rem;
}

.final-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.stat-value {
    color: #4a90e2;
    font-size: 1.5rem;
    font-weight: 700;
}

.stat-value.grade {
    font-size: 3rem;
    color: #50c878;
}

.game-over-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.game-btn {
    padding: 1rem 2rem;
    background: rgba(74, 144, 226, 0.2);
    border: 1px solid rgba(74, 144, 226, 0.5);
    border-radius: 10px;
    color: white;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.game-btn:hover {
    background: rgba(74, 144, 226, 0.4);
    transform: translateY(-2px);
}

/* Instructions */
.instructions {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.instructions-content {
    text-align: center;
    background: rgba(0, 0, 0, 0.4);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(74, 144, 226, 0.3);
    max-width: 500px;
}

.instructions-content h3 {
    color: #4a90e2;
    font-size: 2rem;
    margin-bottom: 2rem;
}

.instruction-list {
    text-align: left;
    margin-bottom: 2rem;
}

.instruction {
    padding: 0.8rem 0;
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.instruction:last-child {
    border-bottom: none;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .note-tracks {
        gap: 0.5rem;
    }
    
    .track {
        width: 80px;
    }
    
    .game-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .score-section {
        gap: 1rem;
        font-size: 0.9rem;
    }
}
