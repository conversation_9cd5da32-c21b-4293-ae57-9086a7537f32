* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    height: 100vh;
    overflow: hidden;
    color: white;
}

.game-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Game Header */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(74, 144, 226, 0.3);
}

.score-section {
    display: flex;
    gap: 2rem;
    font-weight: 600;
}

.score, .combo, .accuracy {
    color: #4a90e2;
}

.song-info {
    text-align: center;
}

.song-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #50c878;
}

.song-artist {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.game-controls {
    display: flex;
    gap: 1rem;
}

.control-btn {
    background: rgba(74, 144, 226, 0.2);
    border: 1px solid rgba(74, 144, 226, 0.5);
    border-radius: 8px;
    padding: 0.5rem;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(74, 144, 226, 0.4);
    transform: scale(1.1);
}

/* Game Area */
.game-area {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem;
    perspective: 1200px;
    background: linear-gradient(180deg, #87CEEB 0%, #4682B4 100%);
}

.table-tennis-scene {
    width: 100%;
    height: 80vh;
    position: relative;
    transform-style: preserve-3d;
}

/* 3D Table */
.table-3d {
    position: absolute;
    width: 80%;
    height: 60%;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%) rotateX(60deg);
    transform-style: preserve-3d;
}

.table-surface {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #0d4f3c 0%, #1a7a5e 50%, #0d4f3c 100%);
    border: 4px solid #ffffff;
    border-radius: 10px;
    position: relative;
    box-shadow:
        0 0 30px rgba(0, 0, 0, 0.3),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
}

.center-line {
    position: absolute;
    top: 48%;
    left: 0;
    right: 0;
    height: 2px;
    background: #ffffff;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

.side-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.side-lines::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.6);
}

.side-lines::after {
    content: '';
    position: absolute;
    bottom: 20%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.6);
}

.table-net {
    position: absolute;
    top: 48%;
    left: 0;
    right: 0;
    height: 20px;
    transform: translateY(-50%) translateZ(10px);
}

.net-mesh {
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.8) 2%,
        transparent 4%,
        rgba(255, 255, 255, 0.8) 6%,
        transparent 8%
    );
    background-size: 20px 100%;
    border-top: 2px solid #ffffff;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.net-posts {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #333;
}

.net-posts::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #333;
}

.net-posts::after {
    content: '';
    position: absolute;
    right: -10px;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #333;
}

.table-legs {
    position: absolute;
    width: 100%;
    height: 100%;
    transform: translateZ(-50px);
}

.table-legs::before,
.table-legs::after {
    content: '';
    position: absolute;
    width: 8px;
    height: 50px;
    background: #8B4513;
    bottom: -50px;
}

.table-legs::before {
    left: 10%;
}

.table-legs::after {
    right: 10%;
}

/* Players */
.computer-player {
    position: absolute;
    top: 15%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: left 0.3s ease;
}

.human-player {
    position: absolute;
    bottom: 15%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: left 0.3s ease;
}

.player-body {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.5));
}

.player-paddle {
    font-size: 2rem;
    transition: transform 0.2s ease;
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
}

.human-player .player-paddle.swing {
    transform: rotate(45deg) scale(1.3);
}

.computer-player .player-paddle.swing {
    transform: rotate(-45deg) scale(1.3);
}

/* Ball */
.ball {
    position: absolute;
    width: 25px;
    height: 25px;
    background: radial-gradient(circle at 30% 30%, #ffffff, #ff6b35);
    border-radius: 50%;
    box-shadow:
        0 0 15px rgba(255, 107, 53, 0.6),
        inset -3px -3px 5px rgba(0, 0, 0, 0.3);
    z-index: 15;
    transition: all 0.1s ease;
    transform-origin: center;
}

.ball.bouncing {
    animation: ballBounce ease-in-out;
}

.ball.hit {
    animation: ballHit 0.8s ease-out forwards;
}

@keyframes ballBounce {
    0% {
        transform: scale(1) translateZ(0px);
    }
    25% {
        transform: scale(1.1) translateZ(15px);
    }
    50% {
        transform: scale(1.2) translateZ(25px);
    }
    75% {
        transform: scale(1.1) translateZ(15px);
    }
    100% {
        transform: scale(1) translateZ(0px);
    }
}

@keyframes ballHit {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.5) rotate(180deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(0.8) rotate(360deg);
        opacity: 1;
    }
}

/* Controls Overlay */
.controls-overlay {
    position: absolute;
    bottom: 5%;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 20;
}

.movement-instruction {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.timing-indicator {
    background: rgba(0, 0, 0, 0.8);
    padding: 1rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    min-width: 300px;
}

.perfect-zone {
    color: #50c878;
    font-weight: 700;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 10px #50c878;
}

.timing-bar {
    width: 100%;
    height: 20px;
    background: linear-gradient(90deg,
        #ff4757 0%,
        #ffa500 25%,
        #50c878 45%,
        #50c878 55%,
        #ffa500 75%,
        #ff4757 100%
    );
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.timing-cursor {
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: white;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
    animation: timingCursor 2s ease-in-out infinite;
}

@keyframes timingCursor {
    0% { left: 0%; }
    50% { left: 96%; }
    100% { left: 0%; }
}

/* Hit Effects */

/* Hit Effects */
.hit-effect {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 700;
    font-size: 2.5rem;
    z-index: 25;
    pointer-events: none;
    animation: hitEffect 1.2s ease-out forwards;
    text-shadow: 0 0 15px currentColor;
}

.hit-effect.perfect { color: #50c878; }
.hit-effect.good { color: #4a90e2; }
.hit-effect.ok { color: #ffa500; }
.hit-effect.miss { color: #ff4757; }

@keyframes hitEffect {
    0% { opacity: 1; transform: translateX(-50%) translateY(0) scale(1); }
    100% { opacity: 0; transform: translateX(-50%) translateY(-100px) scale(2); }
}

/* Song Menu */
.song-menu {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid rgba(74, 144, 226, 0.3);
}

.menu-header h2 {
    color: #4a90e2;
    font-size: 2rem;
}

.close-menu {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-menu:hover {
    background: rgba(255, 77, 87, 0.2);
    color: #ff4757;
}

.song-list {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.song-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(74, 144, 226, 0.3);
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.song-item:hover {
    background: rgba(74, 144, 226, 0.1);
    transform: translateX(10px);
    box-shadow: 0 5px 20px rgba(74, 144, 226, 0.2);
}

.song-info-item {
    flex: 1;
}

.song-name {
    font-size: 1.3rem;
    font-weight: 700;
    color: #50c878;
    margin-bottom: 0.5rem;
}

.song-difficulty {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.song-preview {
    font-size: 2rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.song-item:hover .song-preview {
    opacity: 1;
    transform: scale(1.2);
}

/* Game Over Screen */
.game-over {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.game-over.show {
    display: flex;
    animation: fadeIn 0.5s ease-out;
}

.game-over-content {
    text-align: center;
    background: rgba(0, 0, 0, 0.4);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(74, 144, 226, 0.3);
}

.game-over-content h2 {
    color: #50c878;
    font-size: 2.5rem;
    margin-bottom: 2rem;
}

.final-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.stat-value {
    color: #4a90e2;
    font-size: 1.5rem;
    font-weight: 700;
}

.stat-value.grade {
    font-size: 3rem;
    color: #50c878;
}

.game-over-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.game-btn {
    padding: 1rem 2rem;
    background: rgba(74, 144, 226, 0.2);
    border: 1px solid rgba(74, 144, 226, 0.5);
    border-radius: 10px;
    color: white;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.game-btn:hover {
    background: rgba(74, 144, 226, 0.4);
    transform: translateY(-2px);
}

/* Instructions */
.instructions {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.instructions-content {
    text-align: center;
    background: rgba(0, 0, 0, 0.4);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(74, 144, 226, 0.3);
    max-width: 500px;
}

.instructions-content h3 {
    color: #4a90e2;
    font-size: 2rem;
    margin-bottom: 2rem;
}

.instruction-list {
    text-align: left;
    margin-bottom: 2rem;
}

.instruction {
    padding: 0.8rem 0;
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.instruction:last-child {
    border-bottom: none;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .note-tracks {
        gap: 0.5rem;
    }
    
    .track {
        width: 80px;
    }
    
    .game-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .score-section {
        gap: 1rem;
        font-size: 0.9rem;
    }
}
