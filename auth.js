document.addEventListener('DOMContentLoaded', () => {
    // Tab switching functionality
    const tabButtons = document.querySelectorAll('.tab-btn');
    const authForms = document.querySelectorAll('.auth-form');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');
            
            // Remove active class from all tabs and forms
            tabButtons.forEach(btn => btn.classList.remove('active'));
            authForms.forEach(form => form.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding form
            button.classList.add('active');
            document.getElementById(`${targetTab}-form`).classList.add('active');
        });
    });
    
    // Form submissions
    const signinForm = document.getElementById('signin-email-form');
    const signupForm = document.getElementById('signup-email-form');
    
    signinForm.addEventListener('submit', handleSignIn);
    signupForm.addEventListener('submit', handleSignUp);
    
    // Google Sign-In buttons
    document.getElementById('google-signin').addEventListener('click', handleGoogleSignIn);
    document.getElementById('google-signup').addEventListener('click', handleGoogleSignUp);
    
    // Input animations
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('focus', () => {
            input.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', () => {
            if (!input.value) {
                input.parentElement.classList.remove('focused');
            }
        });
    });
});

// Sign In Handler
async function handleSignIn(e) {
    e.preventDefault();
    
    const email = document.getElementById('signin-email').value;
    const password = document.getElementById('signin-password').value;
    const rememberMe = document.getElementById('remember-me').checked;
    
    const submitBtn = e.target.querySelector('.auth-btn');
    const originalText = submitBtn.textContent;
    
    // Show loading state
    submitBtn.innerHTML = '<div class="loading"></div> Signing In...';
    submitBtn.disabled = true;
    
    try {
        // Simulate API call (replace with actual authentication)
        await simulateAuth(email, password);
        
        // Store user session
        const userData = {
            email: email,
            signedInAt: new Date().toISOString(),
            rememberMe: rememberMe
        };
        
        if (rememberMe) {
            localStorage.setItem('userSession', JSON.stringify(userData));
        } else {
            sessionStorage.setItem('userSession', JSON.stringify(userData));
        }
        
        showMessage('Sign in successful! Redirecting...', 'success');
        
        // Redirect to game after short delay
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1500);
        
    } catch (error) {
        showMessage(error.message, 'error');
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

// Sign Up Handler
async function handleSignUp(e) {
    e.preventDefault();
    
    const username = document.getElementById('signup-username').value;
    const email = document.getElementById('signup-email').value;
    const password = document.getElementById('signup-password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    const agreeTerms = document.getElementById('agree-terms').checked;
    
    // Validation
    if (password !== confirmPassword) {
        showMessage('Passwords do not match!', 'error');
        return;
    }
    
    if (!agreeTerms) {
        showMessage('Please agree to the Terms of Service', 'error');
        return;
    }
    
    if (password.length < 6) {
        showMessage('Password must be at least 6 characters long', 'error');
        return;
    }
    
    const submitBtn = e.target.querySelector('.auth-btn');
    const originalText = submitBtn.textContent;
    
    // Show loading state
    submitBtn.innerHTML = '<div class="loading"></div> Creating Account...';
    submitBtn.disabled = true;
    
    try {
        // Simulate API call (replace with actual registration)
        await simulateRegistration(username, email, password);
        
        showMessage('Account created successfully! Please sign in.', 'success');
        
        // Switch to sign in tab
        setTimeout(() => {
            document.querySelector('[data-tab="signin"]').click();
            document.getElementById('signin-email').value = email;
        }, 1500);
        
    } catch (error) {
        showMessage(error.message, 'error');
    } finally {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

// Google Sign-In Handlers
function handleGoogleSignIn() {
    showMessage('Google Sign-In integration coming soon!', 'info');
    // TODO: Implement actual Google Sign-In
    // This would typically use Google's OAuth 2.0 flow
}

function handleGoogleSignUp() {
    showMessage('Google Sign-Up integration coming soon!', 'info');
    // TODO: Implement actual Google Sign-Up
}

// Google Credential Response Handler (for Google Sign-In button)
function handleCredentialResponse(response) {
    // TODO: Handle the JWT token from Google
    console.log('Google credential response:', response);
    showMessage('Google authentication successful!', 'success');
    
    // Store user session and redirect
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1500);
}

// Simulate authentication (replace with actual API calls)
function simulateAuth(email, password) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            // Simple validation for demo
            if (email && password.length >= 6) {
                resolve({ success: true });
            } else {
                reject(new Error('Invalid email or password'));
            }
        }, 1500);
    });
}

// Simulate registration (replace with actual API calls)
function simulateRegistration(username, email, password) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            // Simple validation for demo
            if (username && email && password.length >= 6) {
                resolve({ success: true });
            } else {
                reject(new Error('Registration failed. Please check your information.'));
            }
        }, 2000);
    });
}

// Message system
function showMessage(text, type = 'info') {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.message');
    existingMessages.forEach(msg => msg.remove());
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = text;
    
    const colors = {
        success: '#50c878',
        error: '#ff4757',
        warning: '#ffa500',
        info: '#4a90e2'
    };
    
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        padding: 1rem 2rem;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        border-radius: 10px;
        border-left: 4px solid ${colors[type]};
        z-index: 1000;
        animation: slideDown 0.5s ease-out;
        font-family: 'Orbitron', monospace;
        font-weight: 600;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    `;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.style.animation = 'slideUp 0.5s ease-out forwards';
        setTimeout(() => messageDiv.remove(), 500);
    }, 3000);
}

// Add CSS animations for messages
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from { transform: translateX(-50%) translateY(-100px); opacity: 0; }
        to { transform: translateX(-50%) translateY(0); opacity: 1; }
    }
    @keyframes slideUp {
        from { transform: translateX(-50%) translateY(0); opacity: 1; }
        to { transform: translateX(-50%) translateY(-100px); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Check if user is already signed in
function checkExistingSession() {
    const sessionData = localStorage.getItem('userSession') || sessionStorage.getItem('userSession');
    if (sessionData) {
        const userData = JSON.parse(sessionData);
        showMessage(`Welcome back, ${userData.email}!`, 'info');
        
        // Auto-redirect after 2 seconds
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
    }
}

// Check for existing session on page load
checkExistingSession();
