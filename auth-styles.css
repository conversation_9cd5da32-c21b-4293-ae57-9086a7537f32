* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    overflow-x: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

/* Background Animation (reused from main page) */
.background-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.stars {
    position: absolute;
    width: 100%;
    height: 100%;
    background: transparent;
}

.stars::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
}

@keyframes sparkle {
    from { transform: translateX(0); }
    to { transform: translateX(-200px); }
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-particles::before,
.floating-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(74, 144, 226, 0.6);
    border-radius: 50%;
    animation: float 15s infinite ease-in-out;
}

.floating-particles::before {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.floating-particles::after {
    top: 60%;
    right: 20%;
    animation-delay: 7s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
    10% { opacity: 1; }
    50% { transform: translateY(-100px) rotate(180deg); opacity: 1; }
    90% { opacity: 1; }
}

/* Auth Container */
.auth-container {
    width: 100%;
    max-width: 450px;
    padding: 2rem;
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-card {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(74, 144, 226, 0.3);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 80px rgba(74, 144, 226, 0.1);
}

/* Logo Section */
.logo-section {
    text-align: center;
    margin-bottom: 2rem;
}

.game-title {
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #4a90e2, #50c878, #4a90e2);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
}

.subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: 1px;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Auth Tabs */
.auth-tabs {
    display: flex;
    margin-bottom: 2rem;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.3);
    padding: 0.3rem;
}

.tab-btn {
    flex: 1;
    padding: 0.8rem;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: rgba(74, 144, 226, 0.2);
    color: #4a90e2;
    box-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
}

.tab-btn:hover:not(.active) {
    color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.05);
}

/* Auth Forms */
.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Social Login */
.social-login {
    margin-bottom: 1.5rem;
}

.social-btn {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    margin-bottom: 1rem;
}

.social-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.social-icon {
    width: 20px;
    height: 20px;
}

/* Divider */
.divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
    color: rgba(255, 255, 255, 0.5);
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
}

.divider span {
    background: rgba(0, 0, 0, 0.4);
    padding: 0 1rem;
    position: relative;
}

/* Input Groups */
.input-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.input-group input {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: white;
    font-family: 'Orbitron', monospace;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
}

.input-group label {
    position: absolute;
    top: 1rem;
    left: 1rem;
    color: rgba(255, 255, 255, 0.5);
    font-size: 1rem;
    pointer-events: none;
    transition: all 0.3s ease;
}

.input-group input:focus + label,
.input-group input:valid + label {
    top: -0.5rem;
    left: 0.5rem;
    font-size: 0.8rem;
    color: #4a90e2;
    background: rgba(0, 0, 0, 0.8);
    padding: 0 0.5rem;
}

.input-highlight {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    width: 0;
    background: #4a90e2;
    transition: width 0.3s ease;
}

.input-group input:focus ~ .input-highlight {
    width: 100%;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.7);
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    margin-right: 0.5rem;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
    background: #4a90e2;
    border-color: #4a90e2;
}

.checkbox-container input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password,
.terms-link {
    color: #4a90e2;
    text-decoration: none;
    transition: color 0.3s ease;
}

.forgot-password:hover,
.terms-link:hover {
    color: #50c878;
}

/* Auth Button */
.auth-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(45deg, #4a90e2, #50c878);
    border: none;
    border-radius: 10px;
    color: white;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.auth-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(74, 144, 226, 0.3);
}

.auth-btn:hover::before {
    left: 100%;
}

.auth-btn:active {
    transform: translateY(0);
}

/* Back to Game */
.back-to-game {
    text-align: center;
    margin-top: 2rem;
}

.back-link {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.back-link:hover {
    color: #4a90e2;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        padding: 1rem;
    }

    .auth-card {
        padding: 2rem;
    }

    .game-title {
        font-size: 2rem;
    }

    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #4a90e2;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
