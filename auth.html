<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Epic Adventure - Sign In</title>
    <link rel="stylesheet" href="auth-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <script src="https://accounts.google.com/gsi/client" async defer></script>
</head>
<body>
    <div class="background-animation">
        <div class="stars"></div>
        <div class="floating-particles"></div>
    </div>
    
    <div class="auth-container">
        <div class="auth-card">
            <div class="logo-section">
                <h1 class="game-title">EPIC ADVENTURE</h1>
                <p class="subtitle">Join the Rhythm Revolution</p>
            </div>
            
            <div class="auth-tabs">
                <button class="tab-btn active" data-tab="signin">Sign In</button>
                <button class="tab-btn" data-tab="signup">Sign Up</button>
            </div>
            
            <!-- Sign In Form -->
            <div class="auth-form active" id="signin-form">
                <div class="social-login">
                    <!-- Google Sign-In will be implemented when you get a client ID -->
                    <button class="social-btn google-btn" id="google-signin">
                        <svg class="social-icon" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Continue with Google
                    </button>
                </div>
                
                <div class="divider">
                    <span>or</span>
                </div>
                
                <form class="email-form" id="signin-email-form">
                    <div class="input-group">
                        <input type="email" id="signin-email" required>
                        <label for="signin-email">Email</label>
                        <div class="input-highlight"></div>
                    </div>
                    
                    <div class="input-group">
                        <input type="password" id="signin-password" required>
                        <label for="signin-password">Password</label>
                        <div class="input-highlight"></div>
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="remember-me">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                        <a href="#" class="forgot-password">Forgot password?</a>
                    </div>
                    
                    <button type="submit" class="auth-btn primary">Sign In</button>
                </form>
            </div>
            
            <!-- Sign Up Form -->
            <div class="auth-form" id="signup-form">
                <div class="social-login">
                    <button class="social-btn google-btn" id="google-signup">
                        <svg class="social-icon" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Sign up with Google
                    </button>
                </div>
                
                <div class="divider">
                    <span>or</span>
                </div>
                
                <form class="email-form" id="signup-email-form">
                    <div class="input-group">
                        <input type="text" id="signup-username" required>
                        <label for="signup-username">Username</label>
                        <div class="input-highlight"></div>
                    </div>
                    
                    <div class="input-group">
                        <input type="email" id="signup-email" required>
                        <label for="signup-email">Email</label>
                        <div class="input-highlight"></div>
                    </div>
                    
                    <div class="input-group">
                        <input type="password" id="signup-password" required>
                        <label for="signup-password">Password</label>
                        <div class="input-highlight"></div>
                    </div>
                    
                    <div class="input-group">
                        <input type="password" id="confirm-password" required>
                        <label for="confirm-password">Confirm Password</label>
                        <div class="input-highlight"></div>
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="agree-terms" required>
                            <span class="checkmark"></span>
                            I agree to the <a href="#" class="terms-link">Terms of Service</a>
                        </label>
                    </div>
                    
                    <button type="submit" class="auth-btn primary">Create Account</button>
                </form>
            </div>
            
            <div class="back-to-game">
                <a href="index.html" class="back-link">← Back to Game</a>
            </div>
        </div>
    </div>
    
    <script src="auth.js"></script>
</body>
</html>
