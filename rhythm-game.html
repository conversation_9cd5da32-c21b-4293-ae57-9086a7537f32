<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Epic Adventure - Table Tennis Rhythm</title>
    <link rel="stylesheet" href="rhythm-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <div class="game-container">
        <!-- Game Header -->
        <div class="game-header">
            <div class="score-section">
                <div class="player-score">You: <span id="player-score">0</span></div>
                <div class="computer-score">CPU: <span id="computer-score">0</span></div>
                <div class="rally-count">Rally: <span id="rally">0</span></div>
            </div>
            <div class="song-info">
                <div class="song-title" id="song-title">Select a Match</div>
                <div class="song-artist" id="song-artist">Opponent</div>
            </div>
            <div class="game-controls">
                <button class="control-btn" id="pause-btn">⏸️</button>
                <button class="control-btn" id="home-btn">🏠</button>
            </div>
        </div>

        <!-- Game Area -->
        <div class="game-area">
            <!-- 3D Table Tennis Scene -->
            <div class="table-tennis-scene">
                <!-- 3D Table -->
                <div class="table-3d">
                    <div class="table-surface">
                        <div class="center-line"></div>
                        <div class="side-lines"></div>
                    </div>
                    <div class="table-net">
                        <div class="net-mesh"></div>
                        <div class="net-posts"></div>
                    </div>
                    <div class="table-legs">
                        <div class="leg leg-1"></div>
                        <div class="leg leg-2"></div>
                        <div class="leg leg-3"></div>
                        <div class="leg leg-4"></div>
                    </div>
                </div>

                <!-- Computer Player -->
                <div class="computer-player" id="computer-player">
                    <div class="player-body">🤖</div>
                    <div class="player-paddle">🏓</div>
                </div>

                <!-- Human Player -->
                <div class="human-player" id="human-player">
                    <div class="player-body">🧑</div>
                    <div class="player-paddle" id="player-paddle">🏓</div>
                </div>

                <!-- Ball -->
                <div class="ball" id="game-ball"></div>

                <!-- Movement Controls -->
                <div class="controls-overlay">
                    <div class="movement-instruction">
                        Use WASD or Arrow Keys to Move | SPACEBAR to Hit
                    </div>
                    <div class="game-status" id="game-status">
                        <div class="serving-info">Get Ready!</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Match Selection Menu -->
        <div class="song-menu" id="song-menu">
            <div class="menu-header">
                <h2>Select Your Opponent</h2>
                <button class="close-menu" id="close-menu">×</button>
            </div>
            <div class="song-list">
                <div class="song-item" data-song="demo1">
                    <div class="song-info-item">
                        <div class="song-name">Rookie Robot</div>
                        <div class="song-difficulty">Beginner</div>
                    </div>
                    <div class="song-preview">🤖</div>
                </div>
                <div class="song-item" data-song="demo2">
                    <div class="song-info-item">
                        <div class="song-name">Speed Demon</div>
                        <div class="song-difficulty">Intermediate</div>
                    </div>
                    <div class="song-preview">⚡</div>
                </div>
                <div class="song-item" data-song="demo3">
                    <div class="song-info-item">
                        <div class="song-name">Spin Master</div>
                        <div class="song-difficulty">Expert</div>
                    </div>
                    <div class="song-preview">🌪️</div>
                </div>
            </div>
        </div>

        <!-- Game Over Screen -->
        <div class="game-over" id="game-over">
            <div class="game-over-content">
                <h2>Match Complete!</h2>
                <div class="final-stats">
                    <div class="stat">
                        <span class="stat-label">Final Score:</span>
                        <span class="stat-value" id="final-player-score">0</span> - <span class="stat-value" id="final-computer-score">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Winner:</span>
                        <span class="stat-value" id="winner">You!</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Longest Rally:</span>
                        <span class="stat-value" id="max-rally">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Total Points:</span>
                        <span class="stat-value" id="total-points">0</span>
                    </div>
                </div>
                <div class="game-over-buttons">
                    <button class="game-btn" id="play-again">Rematch</button>
                    <button class="game-btn" id="select-song">New Opponent</button>
                    <button class="game-btn" id="back-home">Home</button>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="instructions" id="instructions">
            <div class="instructions-content">
                <h3>How to Play Table Tennis Rhythm</h3>
                <div class="instruction-list">
                    <div class="instruction">🏓 Real table tennis - first to 11 points wins!</div>
                    <div class="instruction">⌨️ Use WASD or Arrow Keys to move around your side</div>
                    <div class="instruction">🎯 Get in position and press SPACEBAR to hit</div>
                    <div class="instruction">⚡ Perfect timing = Powerful shot</div>
                    <div class="instruction">💥 Miss the ball = Opponent gets a point</div>
                    <div class="instruction">🏃 Move quickly - ball physics are realistic!</div>
                    <div class="instruction">🎮 Select an opponent to start playing!</div>
                </div>
                <button class="game-btn" id="got-it">Got it!</button>
            </div>
        </div>
    </div>

    <script src="rhythm-game.js"></script>
</body>
</html>
