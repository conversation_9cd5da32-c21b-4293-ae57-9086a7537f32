<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Epic Adventure - Table Tennis Rhythm</title>
    <link rel="stylesheet" href="rhythm-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <div class="game-container">
        <!-- Game Header -->
        <div class="game-header">
            <div class="score-section">
                <div class="score">Score: <span id="score">0</span></div>
                <div class="combo">Rally: <span id="combo">0</span></div>
                <div class="accuracy">Hit Rate: <span id="accuracy">100%</span></div>
            </div>
            <div class="song-info">
                <div class="song-title" id="song-title">Select a Match</div>
                <div class="song-artist" id="song-artist">Opponent</div>
            </div>
            <div class="game-controls">
                <button class="control-btn" id="pause-btn">⏸️</button>
                <button class="control-btn" id="home-btn">🏠</button>
            </div>
        </div>

        <!-- Game Area -->
        <div class="game-area">
            <!-- Table Tennis Court -->
            <div class="table-tennis-court">
                <div class="court-background">
                    <div class="net"></div>
                    <div class="court-lines"></div>
                </div>

                <!-- Player Paddle -->
                <div class="player-paddle" id="player-paddle">🏓</div>

                <!-- Ball Container -->
                <div class="ball-container" id="ball-container"></div>

                <!-- Shot Timing Zones -->
                <div class="timing-zones">
                    <div class="timing-zone" data-key="a" data-shot="forehand">
                        <div class="zone-key">A</div>
                        <div class="zone-label">Forehand</div>
                        <div class="hit-indicator"></div>
                    </div>
                    <div class="timing-zone" data-key="s" data-shot="backhand">
                        <div class="zone-key">S</div>
                        <div class="zone-label">Backhand</div>
                        <div class="hit-indicator"></div>
                    </div>
                    <div class="timing-zone" data-key="d" data-shot="smash">
                        <div class="zone-key">D</div>
                        <div class="zone-label">Smash</div>
                        <div class="hit-indicator"></div>
                    </div>
                    <div class="timing-zone" data-key="f" data-shot="spin">
                        <div class="zone-key">F</div>
                        <div class="zone-label">Spin</div>
                        <div class="hit-indicator"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Match Selection Menu -->
        <div class="song-menu" id="song-menu">
            <div class="menu-header">
                <h2>Select Your Opponent</h2>
                <button class="close-menu" id="close-menu">×</button>
            </div>
            <div class="song-list">
                <div class="song-item" data-song="demo1">
                    <div class="song-info-item">
                        <div class="song-name">Rookie Robot</div>
                        <div class="song-difficulty">Beginner</div>
                    </div>
                    <div class="song-preview">🤖</div>
                </div>
                <div class="song-item" data-song="demo2">
                    <div class="song-info-item">
                        <div class="song-name">Speed Demon</div>
                        <div class="song-difficulty">Intermediate</div>
                    </div>
                    <div class="song-preview">⚡</div>
                </div>
                <div class="song-item" data-song="demo3">
                    <div class="song-info-item">
                        <div class="song-name">Spin Master</div>
                        <div class="song-difficulty">Expert</div>
                    </div>
                    <div class="song-preview">🌪️</div>
                </div>
            </div>
        </div>

        <!-- Game Over Screen -->
        <div class="game-over" id="game-over">
            <div class="game-over-content">
                <h2>Match Complete!</h2>
                <div class="final-stats">
                    <div class="stat">
                        <span class="stat-label">Final Score:</span>
                        <span class="stat-value" id="final-score">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Longest Rally:</span>
                        <span class="stat-value" id="max-combo">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Hit Rate:</span>
                        <span class="stat-value" id="final-accuracy">100%</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Grade:</span>
                        <span class="stat-value grade" id="grade">S</span>
                    </div>
                </div>
                <div class="game-over-buttons">
                    <button class="game-btn" id="play-again">Rematch</button>
                    <button class="game-btn" id="select-song">New Opponent</button>
                    <button class="game-btn" id="back-home">Home</button>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="instructions" id="instructions">
            <div class="instructions-content">
                <h3>How to Play Table Tennis Rhythm</h3>
                <div class="instruction-list">
                    <div class="instruction">🏓 Watch the ball bounce toward you</div>
                    <div class="instruction">⌨️ Press A, S, D, F to hit different shots</div>
                    <div class="instruction">🎯 Perfect timing = Powerful shots</div>
                    <div class="instruction">⚡ Good timing = Normal shots</div>
                    <div class="instruction">💥 Bad timing = Weak shots</div>
                    <div class="instruction">❌ Miss timing = Lost point</div>
                    <div class="instruction">🔥 Build rallies for bonus scores</div>
                    <div class="instruction">🎮 Select an opponent to start!</div>
                </div>
                <button class="game-btn" id="got-it">Got it!</button>
            </div>
        </div>
    </div>

    <script src="rhythm-game.js"></script>
</body>
</html>
