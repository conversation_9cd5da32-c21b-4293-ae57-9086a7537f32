<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Epic Adventure - Rhythm Game</title>
    <link rel="stylesheet" href="rhythm-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <div class="game-container">
        <!-- Game Header -->
        <div class="game-header">
            <div class="score-section">
                <div class="score">Score: <span id="score">0</span></div>
                <div class="combo">Combo: <span id="combo">0</span></div>
                <div class="accuracy">Accuracy: <span id="accuracy">100%</span></div>
            </div>
            <div class="song-info">
                <div class="song-title" id="song-title">Select a Song</div>
                <div class="song-artist" id="song-artist">Artist</div>
            </div>
            <div class="game-controls">
                <button class="control-btn" id="pause-btn">⏸️</button>
                <button class="control-btn" id="home-btn">🏠</button>
            </div>
        </div>

        <!-- Game Area -->
        <div class="game-area">
            <!-- Note Tracks -->
            <div class="note-tracks">
                <div class="track" data-key="a">
                    <div class="track-key">A</div>
                    <div class="hit-zone"></div>
                    <div class="notes-container"></div>
                </div>
                <div class="track" data-key="s">
                    <div class="track-key">S</div>
                    <div class="hit-zone"></div>
                    <div class="notes-container"></div>
                </div>
                <div class="track" data-key="d">
                    <div class="track-key">D</div>
                    <div class="hit-zone"></div>
                    <div class="notes-container"></div>
                </div>
                <div class="track" data-key="f">
                    <div class="track-key">F</div>
                    <div class="hit-zone"></div>
                    <div class="notes-container"></div>
                </div>
            </div>
        </div>

        <!-- Song Selection Menu -->
        <div class="song-menu" id="song-menu">
            <div class="menu-header">
                <h2>Select a Song</h2>
                <button class="close-menu" id="close-menu">×</button>
            </div>
            <div class="song-list">
                <div class="song-item" data-song="demo1">
                    <div class="song-info-item">
                        <div class="song-name">Electronic Dreams</div>
                        <div class="song-difficulty">Easy</div>
                    </div>
                    <div class="song-preview">🎵</div>
                </div>
                <div class="song-item" data-song="demo2">
                    <div class="song-info-item">
                        <div class="song-name">Cyber Pulse</div>
                        <div class="song-difficulty">Medium</div>
                    </div>
                    <div class="song-preview">🎵</div>
                </div>
                <div class="song-item" data-song="demo3">
                    <div class="song-info-item">
                        <div class="song-name">Neon Nights</div>
                        <div class="song-difficulty">Hard</div>
                    </div>
                    <div class="song-preview">🎵</div>
                </div>
            </div>
        </div>

        <!-- Game Over Screen -->
        <div class="game-over" id="game-over">
            <div class="game-over-content">
                <h2>Song Complete!</h2>
                <div class="final-stats">
                    <div class="stat">
                        <span class="stat-label">Final Score:</span>
                        <span class="stat-value" id="final-score">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Max Combo:</span>
                        <span class="stat-value" id="max-combo">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Accuracy:</span>
                        <span class="stat-value" id="final-accuracy">100%</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Grade:</span>
                        <span class="stat-value grade" id="grade">S</span>
                    </div>
                </div>
                <div class="game-over-buttons">
                    <button class="game-btn" id="play-again">Play Again</button>
                    <button class="game-btn" id="select-song">Select Song</button>
                    <button class="game-btn" id="back-home">Home</button>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="instructions" id="instructions">
            <div class="instructions-content">
                <h3>How to Play</h3>
                <div class="instruction-list">
                    <div class="instruction">🎵 Notes fall down the tracks</div>
                    <div class="instruction">⌨️ Press A, S, D, F when notes reach the hit zone</div>
                    <div class="instruction">🎯 Perfect timing gives more points</div>
                    <div class="instruction">🔥 Build combos for bonus scores</div>
                    <div class="instruction">🎮 Click a song to start playing!</div>
                </div>
                <button class="game-btn" id="got-it">Got it!</button>
            </div>
        </div>
    </div>

    <script src="rhythm-game.js"></script>
</body>
</html>
